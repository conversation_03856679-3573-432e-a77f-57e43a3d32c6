# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# ===================================
# Dependencies
# ===================================
/node_modules
/.pnp
.pnp.js
.pnp.cjs

# ===================================
# Next.js
# ===================================
/.next/
/out/

# ===================================
# Production builds
# ===================================
/build
/dist

# ===================================
# Runtime data
# ===================================
pids
*.pid
*.seed
*.pid.lock

# ===================================
# Debug logs
# ===================================
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
lerna-debug.log*

# ===================================
# Environment variables
# ===================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
# Keep example files
!.env.example
!.env.local.example

# ===================================
# Deployment
# ===================================
.vercel
.netlify

# ===================================
# TypeScript
# ===================================
*.tsbuildinfo
next-env.d.ts

# ===================================
# Testing
# ===================================
/coverage
/.nyc_output
.jest-cache

# ===================================
# Package manager cache
# ===================================
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ===================================
# IDE and Editor files
# ===================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===================================
# OS generated files
# ===================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# Temporary files
# ===================================
*.tmp
*.temp
*.log
*.cache

# ===================================
# Optional npm cache directory
# ===================================
.npm

# ===================================
# Optional eslint cache
# ===================================
.eslintcache

# ===================================
# Microbundle cache
# ===================================
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===================================
# Optional REPL history
# ===================================
.node_repl_history

# ===================================
# Output of 'npm pack'
# ===================================
*.tgz

# ===================================
# Yarn Integrity file
# ===================================
.yarn-integrity

# ===================================
# Stores VSCode versions used for testing VSCode extensions
# ===================================
.vscode-test

# ===================================
# Storybook build outputs
# ===================================
.out
.storybook-out
storybook-static

# ===================================
# Turbo
# ===================================
.turbo
