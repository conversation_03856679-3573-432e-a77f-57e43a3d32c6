{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "start": "next start", "check-all": "npm run type-check && npm run lint && npm run format:check"}, "dependencies": {"@radix-ui/react-slot": "1.2.3", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "8.6.0", "lucide-react": "^0.454.0", "motion": "^12.23.12", "next": "15.2.4", "next-themes": "0.4.6", "react": "^19", "react-dom": "^19", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.9", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.33.0", "eslint-config-next": "^15.4.6", "postcss": "^8.5", "prettier": "^3.6.2", "tailwindcss": "^4.1.9", "typescript": "^5"}}