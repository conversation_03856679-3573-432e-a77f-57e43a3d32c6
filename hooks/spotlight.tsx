import React, { createContext, useContext, useEffect, useRef } from "react"
import { useMotionValue, useTransform, motion } from "motion/react"
import { SPOTLIGHT_CONFIG, LAYOUT_CONFIG } from "@/lib/constants"

/**
 * Context value type for sharing mouse position across components
 * Uses refs to avoid re-renders on mouse movement
 */
export type SpotlightContextValue = {
  /** Reference to current mouse X position */
  mouseXRef: React.MutableRefObject<number>
  /** Reference to current mouse Y position */
  mouseYRef: React.MutableRefObject<number>
}

/**
 * React Context for sharing global mouse position without causing re-renders
 */
const SpotlightContext = createContext<SpotlightContextValue | null>(null)

/**
 * Custom hook for tracking global mouse position using refs
 * Avoids React re-renders by storing position in refs instead of state
 *
 * @returns {SpotlightContextValue} Object containing mouse position refs
 */
function useMouseTracking(): SpotlightContextValue {
  const mouseXRef = useRef(0)
  const mouseYRef = useRef(0)

  useEffect(() => {
    const onMove = (e: MouseEvent | PointerEvent) => {
      mouseXRef.current = (e as MouseEvent).clientX
      mouseYRef.current = (e as MouseEvent).clientY
    }

    window.addEventListener("pointermove", onMove, { passive: true })
    return () => {
      window.removeEventListener("pointermove", onMove)
    }
  }, [])

  return { mouseXRef, mouseYRef }
}

export function SpotlightProvider({ children }: { children: React.ReactNode }) {
  const value = useMouseTracking()
  return <SpotlightContext.Provider value={value}>{children}</SpotlightContext.Provider>
}

export function useSpotlightContext(): SpotlightContextValue {
  const ctx = useContext(SpotlightContext)
  if (!ctx) throw new Error("useSpotlightContext must be used within SpotlightProvider")
  return ctx
}

// Layout + centers calculation for the spotlighted labels
export function useLayoutCalculation(params: {
  hostRef: React.RefObject<HTMLElement | null>
  heroRefs: Array<React.RefObject<HTMLElement | null>>
  problemsSelector?: string
}) {
  const { hostRef, heroRefs, problemsSelector = ".network-problem" } = params
  const hostRectRef = useRef<DOMRect | null>(null)
  const centersRef = useRef<Array<{ el: HTMLElement; cx: number; cy: number }>>([])
  const resizeObserverRef = useRef<ResizeObserver | null>(null)
  const placedOnceRef = useRef(false)

  const getProblems = () => {
    const host = hostRef.current
    if (!host) return [] as HTMLElement[]
    return Array.from(host.querySelectorAll<HTMLElement>(problemsSelector))
  }

  const computeCenters = () => {
    const problems = getProblems()
    centersRef.current = problems.map(el => {
      const r = el.getBoundingClientRect()
      return { el, cx: r.left + r.width / 2, cy: r.top + r.height / 2 }
    })
  }

  const placeProblems = () => {
    const host = hostRef.current
    const hostRect = host?.getBoundingClientRect() || null
    if (!host || !hostRect) return
    hostRectRef.current = hostRect

    const problems = getProblems()

    const padding = 24
    const gap = 20 // minimal spacing

    const reservedViewport: Array<{ left: number; top: number; right: number; bottom: number }> = []
    const addRect = (r: DOMRect | null) => {
      if (!r) return
      reservedViewport.push({ left: r.left, top: r.top, right: r.right, bottom: r.bottom })
    }
    for (const ref of heroRefs) addRect(ref.current?.getBoundingClientRect() ?? null)

    const reservedLocalList = reservedViewport
      .map(r => ({
        left: Math.max(0, r.left - hostRect.left),
        top: Math.max(0, r.top - hostRect.top),
        right: Math.min(hostRect.width, r.right - hostRect.left),
        bottom: Math.min(hostRect.height, r.bottom - hostRect.top),
      }))
      .filter(r => r.right > r.left && r.bottom > r.top)

    type NodeBox = { el: HTMLElement; w: number; h: number; x: number; y: number }
    const nodes: NodeBox[] = problems.map(el => ({
      el,
      w: el.offsetWidth || 200,
      h: el.offsetHeight || 40,
      x: 0,
      y: 0,
    })) as NodeBox[]

    const cols = Math.max(6, Math.round(hostRect.width / 220))
    const rows = Math.max(4, Math.round(hostRect.height / LAYOUT_CONFIG.CELL_HEIGHT))
    const cellW = (hostRect.width - padding * 2) / cols
    const cellH = (hostRect.height - padding * 2) / rows

    const occupied: boolean[][] = Array.from({ length: rows }, () =>
      Array<boolean>(cols).fill(false)
    )

    const candidates: Array<{ r: number; c: number }> = []
    for (let r = 0; r < rows; r++) for (let c = 0; c < cols; c++) candidates.push({ r, c })
    for (let i = candidates.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      const candidateI = candidates[i]
      const candidateJ = candidates[j]
      if (candidateI && candidateJ) {
        ;[candidates[i], candidates[j]] = [candidateJ, candidateI]
      }
    }

    const tryPlace = (n: NodeBox): boolean => {
      const spanC = Math.min(cols, Math.max(1, Math.ceil((n.w + gap) / cellW)))
      const spanR = Math.min(rows, Math.max(1, Math.ceil((n.h + gap) / cellH)))
      const order = candidates.slice()
      for (let i = order.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1))
        const orderI = order[i]
        const orderJ = order[j]
        if (orderI && orderJ) {
          ;[order[i], order[j]] = [orderJ, orderI]
        }
      }
      for (const { r, c } of order) {
        if (r + spanR > rows || c + spanC > cols) continue
        let ok = true
        for (let rr = r; rr < r + spanR && ok; rr++)
          for (let cc = c; cc < c + spanC; cc++)
            if (occupied[rr]?.[cc]) {
              ok = false
              break
            }
        if (!ok) continue
        const x0 = padding + c * cellW
        const y0 = padding + r * cellH
        const widthSpan = spanC * cellW
        const heightSpan = spanR * cellH
        const maxX = Math.max(0, widthSpan - n.w)
        const maxY = Math.max(0, heightSpan - n.h)
        n.x = x0 + (maxX > 0 ? Math.random() * maxX : 0)
        n.y = y0 + (maxY > 0 ? Math.random() * maxY : 0)

        const margin = 12
        const labelRect = { left: n.x, top: n.y, right: n.x + n.w, bottom: n.y + n.h }
        const intersectsReserved = reservedLocalList.some(rr => {
          const expanded = {
            left: Math.max(0, rr.left - margin),
            top: Math.max(0, rr.top - margin),
            right: Math.min(hostRect.width, rr.right + margin),
            bottom: Math.min(hostRect.height, rr.bottom + margin),
          }
          return !(
            labelRect.right < expanded.left ||
            labelRect.left > expanded.right ||
            labelRect.bottom < expanded.top ||
            labelRect.top > expanded.bottom
          )
        })
        if (intersectsReserved) continue

        for (let rr = r; rr < r + spanR; rr++)
          for (let cc = c; cc < c + spanC; cc++) {
            const row = occupied[rr]
            if (row) row[cc] = true
          }
        return true
      }
      return false
    }

    for (const n of nodes) {
      if (!tryPlace(n)) {
        n.w = Math.min(n.w, cellW * 2)
        n.h = Math.min(n.h, cellH * 2)
        tryPlace(n)
      }
    }

    for (const n of nodes) {
      n.el.style.left = `${Math.round(n.x)}px`
      n.el.style.top = `${Math.round(n.y)}px`
      n.el.style.textShadow = "none"
    }
  }

  const updateRectAndCenters = (placeIfNeeded = false) => {
    const host = hostRef.current
    if (!host) return
    hostRectRef.current = host.getBoundingClientRect()
    if (placeIfNeeded && !placedOnceRef.current) {
      placeProblems()
      placedOnceRef.current = true
    }
    computeCenters()
  }

  const observeHost = () => {
    const host = hostRef.current
    if (!host || typeof ResizeObserver === "undefined") return
    const ro = new ResizeObserver(() => {
      updateRectAndCenters(false)
    })
    ro.observe(host)
    resizeObserverRef.current = ro
  }

  const disconnectObserver = () => {
    resizeObserverRef.current?.disconnect()
    resizeObserverRef.current = null
  }

  return { hostRectRef, centersRef, updateRectAndCenters, observeHost, disconnectObserver }
}

/**
 * Main spotlight animation hook
 *
 * Creates an interactive spotlight effect that follows the mouse cursor
 * and highlights elements when hovered. Features:
 * - Smooth motion animations using Motion library
 * - GPU-accelerated transforms for performance
 * - Responsive design with layout calculations
 * - Automatic cleanup of event listeners and observers
 *
 * @param {Object} params - Configuration parameters
 * @param {React.RefObject<HTMLDivElement | null>} params.hostRef - Reference to the host container element
 * @param {React.RefObject<HTMLDivElement | null>} params.spotRef - Reference to the spotlight element
 * @param {Array<React.RefObject<HTMLElement | null>>} params.heroRefs - Array of refs to elements that should be highlighted
 * @param {number} [params.innerRadius] - Inner radius of the spotlight fade effect
 * @param {number} [params.outerRadius] - Outer radius of the spotlight fade effect
 * @param {number} [params.radius] - Main spotlight radius
 * @returns {Object} Object containing motion values and transform functions for the spotlight
 */
export function useSpotlight(params: {
  hostRef: React.RefObject<HTMLDivElement | null>
  spotRef: React.RefObject<HTMLDivElement | null>
  heroRefs: Array<React.RefObject<HTMLElement | null>>
  innerRadius?: number
  outerRadius?: number
  radius?: number
}) {
  const {
    hostRef,
    spotRef,
    heroRefs,
    innerRadius = SPOTLIGHT_CONFIG.INNER_RADIUS,
    outerRadius = SPOTLIGHT_CONFIG.OUTER_RADIUS,
    radius = SPOTLIGHT_CONFIG.DEFAULT_RADIUS,
  } = params
  const { mouseXRef, mouseYRef } = useSpotlightContext()

  // Motion values for spotlight position and visibility
  const spotlightX = useMotionValue(-9999)
  const spotlightY = useMotionValue(-9999)
  const spotlightVisible = useMotionValue(0)

  const { hostRectRef, centersRef, updateRectAndCenters, observeHost, disconnectObserver } =
    useLayoutCalculation({ hostRef, heroRefs })

  useEffect(() => {
    const host = hostRef.current
    const spot = spotRef.current
    if (!host || !spot) return

    let rafId = 0
    let needsUpdate = false
    let initTimeout = 0 as unknown as ReturnType<typeof setTimeout>

    const update = () => {
      needsUpdate = false
      const hostRect = hostRectRef.current
      if (!hostRect) return

      const mouseX = mouseXRef.current
      const mouseY = mouseYRef.current
      const hovered =
        mouseX >= hostRect.left &&
        mouseX <= hostRect.right &&
        mouseY >= hostRect.top &&
        mouseY <= hostRect.bottom

      if (!hovered) {
        // Use motion values instead of direct DOM manipulation
        spotlightX.set(-9999)
        spotlightY.set(-9999)
        spotlightVisible.set(0)
        for (const { el } of centersRef.current) {
          // Still need direct manipulation for problem elements as they're not motion components
          el.style.opacity = "0.01"
        }
        return
      }

      const x = mouseX - hostRect.left - radius
      const y = mouseY - hostRect.top - radius
      // Use motion values instead of direct DOM manipulation
      spotlightX.set(x)
      spotlightY.set(y)
      spotlightVisible.set(1)

      for (const { el, cx, cy } of centersRef.current) {
        const dx = mouseX - cx
        const dy = mouseY - cy
        const d = Math.hypot(dx, dy)
        let u: number
        if (d <= innerRadius) u = 1
        else if (d >= outerRadius) u = 0
        else {
          const t = (d - innerRadius) / (outerRadius - innerRadius)
          u = 1 - t * t * (3 - 2 * t)
        }
        const minOpacity = 0.02
        const maxOpacity = 0.9
        const opacity = minOpacity + (maxOpacity - minOpacity) * u
        // Still need direct manipulation for problem elements as they're not motion components
        el.style.opacity = String(opacity)
      }
    }

    const requestUpdate = () => {
      if (!needsUpdate) {
        needsUpdate = true
        rafId = requestAnimationFrame(update)
      }
    }

    const initialize = () => {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          updateRectAndCenters(true) // place once on first init
          requestUpdate()
        })
      })
    }

    // init now and also as a fallback shortly later for layout stability
    initialize()
    initTimeout = setTimeout(initialize, 100)

    const onResize = () => {
      updateRectAndCenters(false) // do NOT re-place on resize; only recompute centers
      requestUpdate()
    }
    const onScroll = () => {
      updateRectAndCenters()
      requestUpdate()
    }

    observeHost()
    window.addEventListener("resize", onResize, { passive: true })
    window.addEventListener("scroll", onScroll, { passive: true })

    const onMove = () => requestUpdate()
    window.addEventListener("pointermove", onMove, { passive: true })

    return () => {
      clearTimeout(initTimeout)
      window.removeEventListener("resize", onResize)
      window.removeEventListener("scroll", onScroll)
      window.removeEventListener("pointermove", onMove)
      cancelAnimationFrame(rafId)
      disconnectObserver()
    }
  }, [
    hostRef,
    spotRef,
    mouseXRef,
    mouseYRef,
    updateRectAndCenters,
    centersRef,
    hostRectRef,
    observeHost,
    disconnectObserver,
    innerRadius,
    outerRadius,
    radius,
  ])

  // Return motion values for use in components
  return {
    spotlightX,
    spotlightY,
    spotlightVisible,
    spotlightTransform: useTransform(
      [spotlightX, spotlightY],
      ([x, y]) => `translate3d(${x}px, ${y}px, 0)`
    ),
  }
}

// Export motion component for use in JSX
export { motion }
