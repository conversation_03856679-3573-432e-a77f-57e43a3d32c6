# Use Cases Section

This document describes the "Use Cases" section added below the "Why 42" section
on the homepage.

- Location: Inserted immediately after the "Why 42" section in `app/page.tsx`.
- Component: `components/use-cases.tsx`
- Library: embla-carousel-react (already present in dependencies).
- Assets: `public/use_case_{1..6}.jpeg`

## Behavior

- Displays a single image at a time with a bottom caption bar.
- Manual navigation via Prev/Next buttons (desktop overlay controls and mobile
  buttons beneath).
- Smooth sliding transitions using Embla.
- Responsive: full-width on small screens with 320px image height, 520px on
  desktop.

## Content Mapping

- Each slide uses the following description strings:
  1. TCP HANDSHAKE FAILED, SSL HANDSHAKE FAILED, VPN TUNNEL TIMEOUT, API
     AUTHENTICATION FAILED
  2. DNS TIMEOUT, HTTP REQUEST TIMEOUT, TCP CONNECTION TIMEOUT, API CALL TIMEOUT
  3. PACKET LOSS, OUT OF ORDER ONE WAY TRAFFIC, LINK DOWN, INCOMPLETE TRANSFER
  4. VOIP HIGH LATENCY, JITTE<PERSON>, RTP PACKET LOSS, CHOPPY AUDIO, ONE-WAY AUDIO
  5. ROUTING LOOP, BROADCAST STORM, INFINITE REDIRECTS, DNS LOOP
  6. DDOS ATTACK, TRAFFIC FLOODING, TRAFFIC SPIKE, DOWNLOAD SURGE

## Styling Notes

- Caption bar color: cyan-800 with slight transparency to echo site palette.
- Rounded container and shadow to match existing card styles.
- Uses Tailwind classes; no global CSS added.

## Usage

Import and place the component where needed:

\`\`\`tsx import UseCases from "@/components/use-cases"

<UseCases />
\`\`\`
