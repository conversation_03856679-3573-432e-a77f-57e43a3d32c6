"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ErrorBoundary } from "./error-boundary"

/**
 * Test component that can throw errors on demand to test error boundaries
 * Only available in development mode
 */
function ErrorThrowingComponent({ shouldThrow }: { shouldThrow: boolean }) {
  if (shouldThrow) {
    throw new Error("Test error thrown by ErrorThrowingComponent")
  }

  return (
    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
      <h3 className="text-green-800 font-semibold mb-2">✅ Component Working</h3>
      <p className="text-green-700 text-sm">
        This component is working normally. Click the button below to trigger an error and test the
        error boundary.
      </p>
    </div>
  )
}

/**
 * Error boundary test component for development
 * This component allows developers to test error boundaries by triggering errors on demand
 */
export function ErrorBoundaryTest() {
  const [shouldThrow, setShouldThrow] = useState(false)

  // Only show in development
  if (process.env.NODE_ENV !== "development") {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <div className="bg-white border border-slate-200 rounded-lg shadow-lg p-4">
        <h3 className="font-semibold text-slate-900 mb-3">🧪 Error Boundary Test</h3>

        <ErrorBoundary
          onError={(error, errorInfo) => {
            console.error("Test error caught by ErrorBoundary:", error)
            console.error("Error info:", errorInfo)
          }}
        >
          <ErrorThrowingComponent shouldThrow={shouldThrow} />
        </ErrorBoundary>

        <div className="mt-3 space-y-2">
          <Button
            onClick={() => setShouldThrow(true)}
            variant="destructive"
            size="sm"
            className="w-full"
          >
            Trigger Error
          </Button>
          <Button
            onClick={() => setShouldThrow(false)}
            variant="outline"
            size="sm"
            className="w-full"
          >
            Reset Component
          </Button>
        </div>

        <p className="text-xs text-slate-500 mt-2">
          Development only - tests error boundary functionality
        </p>
      </div>
    </div>
  )
}
