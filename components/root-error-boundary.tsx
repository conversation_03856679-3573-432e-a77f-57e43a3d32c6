"use client"

import React from "react"
import { ErrorBoundary } from "./error-boundary"

/**
 * Root-level error boundary wrapper for the entire application
 * This component is client-side only and wraps the app content
 */
export function RootErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        // In production, send to error tracking service like Sentry
        console.error("Root layout error:", error)
        console.error("Component stack:", errorInfo.componentStack)
      }}
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-slate-50">
          <div className="text-center max-w-lg mx-auto p-8">
            <div className="text-8xl mb-6">🚨</div>
            <h1 className="text-4xl font-bold text-slate-900 mb-4">Application Error</h1>
            <p className="text-slate-600 mb-8 text-lg">
              We&apos;re sorry, but something went wrong. Our team has been notified and is working
              to fix the issue.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Reload Application
            </button>
          </div>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  )
}
