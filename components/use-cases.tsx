"use client"

import useEmblaCarousel from "embla-carousel-react"
import { useCallback, useEffect, useState } from "react"
import Image from "next/image"
import { LoadingSkeleton } from "@/components/ui/loading-spinner"

/**
 * Use case data structure for the carousel
 */
interface UseCase {
  /** Image source path */
  src: string
  /** Alt text for accessibility */
  alt: string
  /** Technical description of the use case */
  description: string
  /** Creative subtitle for the use case */
  subtitle: string
}

/**
 * Array of use cases showcasing different network troubleshooting scenarios
 */
const useCases: UseCase[] = [
  {
    src: "/use_case_1.jpeg",
    alt: "Use case 1",
    description:
      "TCP Handshake Failed, SSL Handshake Failed, VPN Tunnel Timeout, API Authentication Failed",
    subtitle: "The hands stretch across the void, but the handshake never completes.",
  },
  {
    src: "/use_case_2.jpeg",
    alt: "Use case 2",
    description: "DNS Timeout, HTTP Request Timeout, TCP Connection Timeout, API Call Timeout",
    subtitle: "Endless waiting bends time, memory becomes eternal, and users collapse.",
  },
  {
    src: "/use_case_3.jpeg",
    alt: "Use case 3",
    description: "Packet Loss, Out Of Order, One Way Traffic, Link Down, Incomplete Transfer",
    subtitle: "The statue stands proud, yet the missing arms reveal the loss within.",
  },
  {
    src: "/use_case_4.jpeg",
    alt: "Use case 4",
    description: "VoIP High Latency, Jitter, RTP Packet Loss, Choppy Audio, One-Way Audio",
    subtitle: "A broken voice echoes in distortion, crying for clarity that never comes.",
  },
  {
    src: "/use_case_5.jpeg",
    alt: "Use case 5",
    description: "Routing Loop, Broadcast Storm, Infinite Redirects, DNS Loop",
    subtitle: "Packets wander the endless stairways, trapped in loops without escape.",
  },
  {
    src: "/use_case_6.jpeg",
    alt: "Use case 6",
    description: "DDoS Attack, Traffic Flooding, Traffic Spike, Download Surge",
    subtitle: "The giant wave of traffic crashes down, drowning the fragile link.",
  },
]

/**
 * UseCases Component
 *
 * A responsive carousel component that showcases different network troubleshooting
 * use cases with images, descriptions, and navigation controls. Features:
 * - Touch/swipe navigation on mobile
 * - Keyboard navigation (Arrow Left/Right)
 * - Loading states with skeleton placeholders
 * - Accessibility support with ARIA labels
 * - Auto-looping carousel
 *
 * @returns {JSX.Element} The rendered use cases carousel
 */
export default function UseCases() {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, align: "start" })
  const [currentIndex, setCurrentIndex] = useState(0)
  const [imagesLoaded, setImagesLoaded] = useState<boolean[]>(
    new Array(useCases.length).fill(false)
  )

  const scrollPrev = useCallback(() => emblaApi?.scrollPrev(), [emblaApi])
  const scrollNext = useCallback(() => emblaApi?.scrollNext(), [emblaApi])

  /**
   * Handles keyboard navigation for the carousel
   * @param {React.KeyboardEvent} event - The keyboard event
   */
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === "ArrowLeft") {
        event.preventDefault()
        scrollPrev()
      } else if (event.key === "ArrowRight") {
        event.preventDefault()
        scrollNext()
      }
    },
    [scrollPrev, scrollNext]
  )

  /**
   * Handles image load events to update loading states
   * @param {number} index - The index of the loaded image
   */
  const handleImageLoad = useCallback((index: number) => {
    setImagesLoaded(prev => {
      const newState = [...prev]
      newState[index] = true
      return newState
    })
  }, [])

  const onSelect = useCallback(() => {
    if (!emblaApi) return
    setCurrentIndex(emblaApi.selectedScrollSnap())
  }, [emblaApi])

  useEffect(() => {
    if (!emblaApi) return

    onSelect()
    emblaApi.on("select", onSelect)
    emblaApi.on("reInit", onSelect)

    return () => {
      emblaApi.off("select", onSelect)
      emblaApi.off("reInit", onSelect)
    }
  }, [emblaApi, onSelect])

  return (
    <section id="use-cases" className="bg-white py-20">
      <div className="container mx-auto px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-10">
            <h2 className="font-space-grotesk font-bold text-5xl text-cyan-800 mb-4 tracking-tight">
              Use Cases
            </h2>
            <p className="font-space-grotesk text-xl text-slate-600 font-light transition-all duration-500 ease-in-out">
              {useCases[currentIndex]?.subtitle ||
                "When the network stops, so does everything else."}
            </p>
          </div>

          <div className="relative">
            {/* Desktop overlay controls - Added frosted glass effect to circular buttons */}
            <button
              aria-label="Previous use case"
              onClick={scrollPrev}
              className="hidden md:flex absolute left-4 top-1/2 -translate-y-1/2 z-20 bg-white/20 backdrop-blur-md hover:bg-white/30 text-cyan-800 rounded-full w-12 h-12 items-center justify-center shadow-lg border border-white/30 cursor-pointer active:scale-95 transition-all"
            >
              <span className="text-2xl leading-none">‹</span>
            </button>
            <button
              aria-label="Next use case"
              onClick={scrollNext}
              className="hidden md:flex absolute right-4 top-1/2 -translate-y-1/2 z-20 bg-white/20 backdrop-blur-md hover:bg-white/30 text-cyan-800 rounded-full w-12 h-12 items-center justify-center shadow-lg border border-white/30 cursor-pointer active:scale-95 transition-all"
            >
              <span className="text-2xl leading-none">›</span>
            </button>

            <div
              className="overflow-hidden shadow-xl"
              onKeyDown={handleKeyDown}
              tabIndex={0}
              role="region"
              aria-label="Use cases carousel"
              aria-live="polite"
              aria-atomic="true"
            >
              <div className="embla__viewport overflow-hidden" ref={emblaRef}>
                <div className="embla__container flex">
                  {useCases.map((uc, idx) => (
                    <div key={idx} className="embla__slide relative flex-[0_0_100%] min-w-0">
                      {!imagesLoaded[idx] && (
                        <div className="absolute inset-0 z-10">
                          <LoadingSkeleton className="w-full h-[320px] md:h-[520px]" />
                        </div>
                      )}
                      <Image
                        src={uc.src || "/placeholder.svg"}
                        alt={uc.alt}
                        width={800}
                        height={520}
                        className={`w-full h-[320px] md:h-[520px] object-cover select-none transition-opacity duration-300 ${
                          imagesLoaded[idx] ? "opacity-100" : "opacity-0"
                        }`}
                        onLoad={() => handleImageLoad(idx)}
                        priority={idx === 0} // Prioritize loading the first image
                      />

                      {/* Caption Bar */}
                      <div className="absolute bottom-0 left-0 right-0">
                        <div className="bg-cyan-800/90 backdrop-blur-sm text-white px-4 md:px-8 py-3 md:py-5">
                          <p className="text-center font-space-grotesk font-semibold italic tracking-wide text-[12px] md:text-lg leading-snug">
                            {uc.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Mobile controls - Removed rounded corners from mobile buttons */}
            <div className="md:hidden flex items-center justify-between gap-3 mt-4">
              <button
                onClick={scrollPrev}
                aria-label="Previous use case"
                className="flex-1 bg-cyan-700 text-white py-2 font-space-grotesk font-medium hover:bg-cyan-600 transition-all cursor-pointer active:scale-95"
              >
                Previous
              </button>
              <button
                onClick={scrollNext}
                aria-label="Next use case"
                className="flex-1 bg-emerald-600 text-white py-2 font-space-grotesk font-medium hover:bg-emerald-500 transition-all cursor-pointer active:scale-95"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
