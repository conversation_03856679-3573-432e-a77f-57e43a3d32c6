/** @type {import('next').NextConfig} */
const nextConfig = {
  // ESLint and TypeScript error checking are now enabled during builds
  // This ensures code quality and prevents faulty deployments
  eslint: {
    // Explicitly specify ESLint configuration file
    dirs: ['app', 'components', 'hooks', 'lib'],
  },
  // Image optimization is now enabled for better performance
  // images: {
  //   unoptimized: true, // Removed - enables Next.js image optimization
  // },
}

export default nextConfig
