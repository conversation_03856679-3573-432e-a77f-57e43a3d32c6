{"semi": false, "trailingComma": "es5", "singleQuote": false, "printWidth": 100, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": false, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "overrides": [{"files": ["*.md", "*.mdx"], "options": {"printWidth": 80, "proseWrap": "always"}}, {"files": ["*.json", "*.jsonc"], "options": {"printWidth": 120}}, {"files": ["*.yml", "*.yaml"], "options": {"tabWidth": 2, "singleQuote": true}}]}