import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Space_Grotesk, DM_Sans, Roboto_Mono } from "next/font/google"
import "./globals.css"
import { RootErrorBoundary } from "@/components/root-error-boundary"

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-space-grotesk",
})

const dmSans = DM_Sans({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-dm-sans",
})

const robotoMono = Roboto_Mono({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-roboto-mono",
})

export const metadata: Metadata = {
  title: "42 - AI Troubleshooting Agent",
  description:
    "Empower your troubleshooting with intelligent AI diagnostics. Advanced network analysis, real-time monitoring, and automated problem resolution.",
  keywords: [
    "AI troubleshooting",
    "network diagnostics",
    "automated monitoring",
    "IT support",
    "network analysis",
    "system diagnostics",
  ],
  authors: [{ name: "<PERSON><PERSON> <PERSON>" }],
  creator: "<PERSON><PERSON>",
  publisher: "Go42",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || "https://go42.ai"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "42 - AI Troubleshooting Agent",
    description:
      "Empower your troubleshooting with intelligent AI diagnostics. Advanced network analysis, real-time monitoring, and automated problem resolution.",
    url: process.env.NEXT_PUBLIC_APP_URL || "https://go42.ai",
    siteName: "42 - AI Troubleshooting Agent",
    images: [
      {
        url: "/42logo-figma.svg",
        width: 1200,
        height: 630,
        alt: "42 - AI Troubleshooting Agent Logo",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "42 - AI Troubleshooting Agent",
    description:
      "Empower your troubleshooting with intelligent AI diagnostics. Advanced network analysis, real-time monitoring, and automated problem resolution.",
    images: ["/42logo-figma.svg"],
    creator: `@${process.env.NEXT_PUBLIC_TWITTER_HANDLE || "go42ai"}`,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
    yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION,
  },
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html
      lang="en"
      className={`${spaceGrotesk.variable} ${dmSans.variable} ${robotoMono.variable} antialiased`}
    >
      <body className="font-sans">
        <RootErrorBoundary>{children}</RootErrorBoundary>
      </body>
    </html>
  )
}
