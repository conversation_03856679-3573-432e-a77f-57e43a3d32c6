#!/bin/bash

# Go42 项目未使用依赖清理脚本
# 基于 depcheck 分析结果，安全移除确认未使用的依赖

set -e

echo "🧹 Go42 项目依赖清理脚本"
echo "=========================="
echo ""

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 备份 package.json
echo "📋 备份 package.json..."
cp package.json package.json.backup
echo "✅ 已创建备份: package.json.backup"
echo ""

# 运行 depcheck 确认当前状态
echo "🔍 运行 depcheck 确认当前未使用的依赖..."
echo ""
npx depcheck
echo ""

# 自动继续清理 (跳过用户确认)
echo "✅ 自动继续清理未使用的依赖..."

echo ""
echo "🚀 开始清理未使用的依赖..."
echo ""

# 第一步: 移除确认未使用的 Radix UI 组件
echo "1️⃣ 移除未使用的 Radix UI 组件..."
npm uninstall \
    @radix-ui/react-accordion \
    @radix-ui/react-alert-dialog \
    @radix-ui/react-aspect-ratio \
    @radix-ui/react-avatar \
    @radix-ui/react-checkbox \
    @radix-ui/react-collapsible \
    @radix-ui/react-context-menu \
    @radix-ui/react-dialog \
    @radix-ui/react-dropdown-menu \
    @radix-ui/react-hover-card \
    @radix-ui/react-label \
    @radix-ui/react-menubar \
    @radix-ui/react-navigation-menu \
    @radix-ui/react-popover \
    @radix-ui/react-progress \
    @radix-ui/react-radio-group \
    @radix-ui/react-scroll-area \
    @radix-ui/react-select \
    @radix-ui/react-separator \
    @radix-ui/react-slider \
    @radix-ui/react-switch \
    @radix-ui/react-tabs \
    @radix-ui/react-toast \
    @radix-ui/react-toggle \
    @radix-ui/react-toggle-group \
    @radix-ui/react-tooltip

echo "✅ Radix UI 组件清理完成"
echo ""

# 第二步: 移除确认未使用的表单和工具库
echo "2️⃣ 移除未使用的表单和工具库..."
npm uninstall \
    @hookform/resolvers \
    react-hook-form \
    zod \
    cmdk \
    date-fns \
    input-otp \
    react-day-picker \
    sonner \
    vaul \
    recharts \
    react-resizable-panels \
    geist

echo "✅ 表单和工具库清理完成"
echo ""

# 第三步: 移除确认未使用的开发依赖
echo "3️⃣ 移除未使用的开发依赖..."
npm uninstall tw-animate-css

echo "✅ 开发依赖清理完成"
echo ""

# 验证构建是否正常
echo "🔧 验证构建是否正常..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功! 依赖清理完成"
    echo ""
    echo "📊 清理统计:"
    echo "   - 移除了 25+ 个 Radix UI 组件"
    echo "   - 移除了 12 个未使用的工具库"
    echo "   - 移除了 1 个未使用的开发依赖"
    echo ""
    echo "🎉 项目依赖已优化，包大小显著减少!"
    echo ""
    echo "💡 建议:"
    echo "   - 运行 'npm run check-all' 确保代码质量"
    echo "   - 删除备份文件: rm package.json.backup"
    echo "   - 提交更改到版本控制"
else
    echo "❌ 构建失败! 正在恢复备份..."
    mv package.json.backup package.json
    npm install
    echo "🔄 已恢复到清理前的状态"
    echo ""
    echo "⚠️  请检查是否有遗漏的依赖使用情况"
    exit 1
fi

echo ""
echo "🏁 依赖清理脚本执行完成!"
